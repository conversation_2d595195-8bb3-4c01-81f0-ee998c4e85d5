@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 - 简化版本 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 矩阵专用样式 */
.matrix-cell {
  transition: all 0.1s ease;
  box-sizing: border-box;
  user-select: none;
  cursor: pointer;
}

.matrix-cell:hover {
  transform: scale(1.05);
  z-index: 10;
}

.matrix-cell.selected {
  box-shadow: 0 0 0 2px #3b82f6;
}

.matrix-cell.coordinate-mode {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 10px;
}

.matrix-cell.color-mode {
  font-weight: bold;
  text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
}

.matrix-cell.value-mode {
  font-family: 'Arial', sans-serif;
  font-weight: bold;
}

.matrix-cell.word-mode {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  line-height: 1.2;
}

/* 性能优化 */
.matrix-container {
  will-change: transform;
  contain: layout style paint;
}
