/**
 * 矩阵控制组件
 * 🎯 核心价值：纯UI控制组件，配置驱动，零业务逻辑
 * 📦 功能范围：模式切换、配置调整、状态显示
 * 🔄 架构设计：完全无状态组件，所有逻辑通过状态管理注入
 */

'use client';

import React, { memo, useCallback } from 'react';
import { useMatrixStore } from '@/core/matrix/MatrixStore';
import type { BusinessMode, MatrixConfig } from '@/core/matrix/MatrixTypes';

// ===== 组件属性 =====

interface ControlsProps {
  /** 自定义样式 */
  className?: string;
  style?: React.CSSProperties;
  
  /** 显示配置 */
  showModeSelector?: boolean;
  showConfigPanel?: boolean;
  showStatusBar?: boolean;
  
  /** 事件回调 */
  onModeChange?: (mode: BusinessMode) => void;
  onConfigChange?: (config: Partial<MatrixConfig>) => void;
  onReset?: () => void;
}

// ===== 模式选择器 =====

const ModeSelector: React.FC<{
  currentMode: BusinessMode;
  onModeChange: (mode: BusinessMode) => void;
}> = memo(({ currentMode, onModeChange }) => {
  const modes: Array<{ value: BusinessMode; label: string; shortcut: string }> = [
    { value: 'coordinate', label: '坐标模式', shortcut: 'Ctrl+1' },
    { value: 'color', label: '颜色模式', shortcut: 'Ctrl+2' },
    { value: 'value', label: '数值模式', shortcut: 'Ctrl+3' },
    { value: 'word', label: '词语模式', shortcut: 'Ctrl+4' },
  ];
  
  return (
    <div className="mode-selector">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        显示模式
      </label>
      <div className="grid grid-cols-2 gap-2">
        {modes.map(({ value, label, shortcut }) => (
          <button
            key={value}
            onClick={() => onModeChange(value)}
            className={`
              px-3 py-2 text-sm rounded-md border transition-colors
              ${currentMode === value
                ? 'bg-blue-500 text-white border-blue-500'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }
            `}
            title={shortcut}
          >
            {label}
          </button>
        ))}
      </div>
    </div>
  );
});

ModeSelector.displayName = 'ModeSelector';

// ===== 配置面板 =====

const ConfigPanel: React.FC<{
  config: MatrixConfig;
  onConfigChange: (updates: Partial<MatrixConfig>) => void;
}> = memo(({ config, onConfigChange }) => {
  const handleToggle = useCallback((key: keyof MatrixConfig) => {
    onConfigChange({ [key]: !config[key] });
  }, [config, onConfigChange]);
  
  const handleSliderChange = useCallback((key: keyof MatrixConfig, value: number) => {
    onConfigChange({ [key]: value });
  }, [onConfigChange]);
  
  const handleThemeChange = useCallback((theme: 'light' | 'dark') => {
    onConfigChange({ theme });
  }, [onConfigChange]);
  
  return (
    <div className="config-panel space-y-4">
      <h3 className="text-sm font-medium text-gray-700">显示配置</h3>
      
      {/* 显示选项 */}
      <div className="space-y-2">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={config.showGrid}
            onChange={() => handleToggle('showGrid')}
            className="mr-2"
          />
          <span className="text-sm text-gray-600">显示网格</span>
        </label>
        
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={config.showCoordinates}
            onChange={() => handleToggle('showCoordinates')}
            className="mr-2"
          />
          <span className="text-sm text-gray-600">显示坐标</span>
        </label>
        
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={config.showColors}
            onChange={() => handleToggle('showColors')}
            className="mr-2"
          />
          <span className="text-sm text-gray-600">显示颜色</span>
        </label>
        
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={config.showValues}
            onChange={() => handleToggle('showValues')}
            className="mr-2"
          />
          <span className="text-sm text-gray-600">显示数值</span>
        </label>
        
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={config.showWords}
            onChange={() => handleToggle('showWords')}
            className="mr-2"
          />
          <span className="text-sm text-gray-600">显示词语</span>
        </label>
      </div>
      
      {/* 尺寸配置 */}
      <div className="space-y-3">
        <div>
          <label className="block text-sm text-gray-600 mb-1">
            单元格大小: {config.cellSize}px
          </label>
          <input
            type="range"
            min="10"
            max="50"
            value={config.cellSize}
            onChange={(e) => handleSliderChange('cellSize', parseInt(e.target.value))}
            className="w-full"
          />
        </div>
        
        <div>
          <label className="block text-sm text-gray-600 mb-1">
            间距: {config.gap}px
          </label>
          <input
            type="range"
            min="0"
            max="5"
            value={config.gap}
            onChange={(e) => handleSliderChange('gap', parseInt(e.target.value))}
            className="w-full"
          />
        </div>
      </div>
      
      {/* 主题选择 */}
      <div>
        <label className="block text-sm text-gray-600 mb-2">主题</label>
        <div className="flex space-x-2">
          <button
            onClick={() => handleThemeChange('light')}
            className={`
              px-3 py-1 text-sm rounded border
              ${config.theme === 'light'
                ? 'bg-blue-500 text-white border-blue-500'
                : 'bg-white text-gray-700 border-gray-300'
              }
            `}
          >
            浅色
          </button>
          <button
            onClick={() => handleThemeChange('dark')}
            className={`
              px-3 py-1 text-sm rounded border
              ${config.theme === 'dark'
                ? 'bg-blue-500 text-white border-blue-500'
                : 'bg-white text-gray-700 border-gray-300'
              }
            `}
          >
            深色
          </button>
        </div>
      </div>
    </div>
  );
});

ConfigPanel.displayName = 'ConfigPanel';

// ===== 状态栏 =====

const StatusBar: React.FC<{
  mode: BusinessMode;
  selectedCount: number;
  totalCells: number;
}> = memo(({ mode, selectedCount, totalCells }) => {
  const modeLabels = {
    coordinate: '坐标',
    color: '颜色',
    value: '数值',
    word: '词语',
  };
  
  return (
    <div className="status-bar flex items-center justify-between p-2 bg-gray-50 border-t text-sm text-gray-600">
      <div className="flex items-center space-x-4">
        <span>模式: {modeLabels[mode]}</span>
        <span>已选择: {selectedCount}</span>
        <span>总计: {totalCells}</span>
      </div>
      <div className="text-xs text-gray-500">
        使用 Ctrl**** 快速切换模式
      </div>
    </div>
  );
});

StatusBar.displayName = 'StatusBar';

// ===== 主控制组件 =====

const ControlsComponent: React.FC<ControlsProps> = ({
  className = '',
  style,
  showModeSelector = true,
  showConfigPanel = true,
  showStatusBar = true,
  onModeChange,
  onConfigChange,
  onReset,
}) => {
  const {
    data,
    config,
    setMode,
    updateConfig,
    resetConfig,
    initializeMatrix
  } = useMatrixStore();

  const mode = config.mode;
  const selectedCells = data.selectedCells;
  
  // 处理模式切换
  const handleModeChange = useCallback((newMode: BusinessMode) => {
    setMode(newMode);
    onModeChange?.(newMode);
  }, [setMode, onModeChange]);
  
  // 处理配置更新
  const handleConfigChange = useCallback((updates: Partial<MatrixConfig>) => {
    updateConfig(updates);
    onConfigChange?.(updates);
  }, [updateConfig, onConfigChange]);
  
  // 处理重置
  const handleReset = useCallback(() => {
    resetConfig();
    initializeMatrix();
    onReset?.();
  }, [resetConfig, initializeMatrix, onReset]);
  
  return (
    <div className={`controls-container ${className}`} style={style}>
      <div className="p-4 space-y-6">
        {/* 模式选择器 */}
        {showModeSelector && (
          <ModeSelector
            currentMode={mode}
            onModeChange={handleModeChange}
          />
        )}
        
        {/* 配置面板 */}
        {showConfigPanel && (
          <ConfigPanel
            config={config}
            onConfigChange={handleConfigChange}
          />
        )}
        
        {/* 操作按钮 */}
        <div className="space-y-2">
          <button
            onClick={handleReset}
            className="w-full px-4 py-2 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          >
            重置矩阵
          </button>
        </div>
      </div>
      
      {/* 状态栏 */}
      {showStatusBar && (
        <StatusBar
          mode={mode}
          selectedCount={selectedCells.size}
          totalCells={1089}
        />
      )}
    </div>
  );
};

// ===== 性能优化 =====

const Controls = memo(ControlsComponent);

Controls.displayName = 'Controls';

export default Controls;
