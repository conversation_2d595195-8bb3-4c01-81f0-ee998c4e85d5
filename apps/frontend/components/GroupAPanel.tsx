/**
 * A组控制面板组件
 * 🎯 核心价值：A组数据的可视化控制和交互
 * 📦 功能范围：颜色选择、层级控制、数据展示
 * 🔄 架构设计：纯渲染组件，数据驱动，支持灰度模式
 */

import React, { memo, useMemo } from 'react';
import { useMatrixStore } from '@/core/matrix/MatrixStore';
import type { ColorType, DataLevel } from '@/core/matrix/MatrixTypes';

// ===== 类型定义 =====

interface GroupAPanelProps {
  /** 容器样式 */
  className?: string;
  style?: React.CSSProperties;
  
  /** 显示配置 */
  showColorButtons?: boolean;
  showLevelFilter?: boolean;
  showStatistics?: boolean;
  
  /** 交互回调 */
  onColorToggle?: (color: ColorType) => void;
  onLevelChange?: (level: DataLevel | null) => void;
}

// ===== 颜色配置 =====

const COLOR_CONFIG: Record<ColorType, { label: string; bgColor: string; textColor: string }> = {
  red: { label: '红色', bgColor: 'bg-red-500', textColor: 'text-white' },
  cyan: { label: '青色', bgColor: 'bg-cyan-500', textColor: 'text-white' },
  yellow: { label: '黄色', bgColor: 'bg-yellow-500', textColor: 'text-black' },
  purple: { label: '紫色', bgColor: 'bg-purple-500', textColor: 'text-white' },
  orange: { label: '橙色', bgColor: 'bg-orange-500', textColor: 'text-white' },
  green: { label: '绿色', bgColor: 'bg-green-500', textColor: 'text-white' },
  blue: { label: '蓝色', bgColor: 'bg-blue-500', textColor: 'text-white' },
  pink: { label: '粉色', bgColor: 'bg-pink-500', textColor: 'text-white' },
};

const COLORS: ColorType[] = ['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'];
const LEVELS: DataLevel[] = [1, 2, 3, 4];

// ===== 子组件 =====

/** 颜色按钮组件 */
const ColorButton: React.FC<{
  color: ColorType;
  count: number;
  isActive?: boolean;
  onClick: () => void;
}> = memo(({ color, count, isActive = false, onClick }) => {
  const config = COLOR_CONFIG[color];
  
  return (
    <button
      onClick={onClick}
      className={`
        relative px-3 py-2 rounded-lg border-2 transition-all duration-200
        ${config.bgColor} ${config.textColor}
        ${isActive 
          ? 'border-blue-400 ring-2 ring-blue-200 scale-105' 
          : 'border-gray-300 hover:border-gray-400'
        }
        hover:scale-105 active:scale-95
      `}
      title={`${config.label} (${count} 个数据点)`}
    >
      <div className="flex flex-col items-center">
        <span className="text-sm font-medium">{config.label}</span>
        <span className="text-xs opacity-80">{count}</span>
      </div>
    </button>
  );
});

ColorButton.displayName = 'ColorButton';

/** 层级过滤器组件 */
const LevelFilter: React.FC<{
  currentLevel: DataLevel | null;
  levelCounts: Record<DataLevel, number>;
  onChange: (level: DataLevel | null) => void;
}> = memo(({ currentLevel, levelCounts, onChange }) => {
  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium text-gray-700">层级过滤</h3>
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => onChange(null)}
          className={`
            px-3 py-1 rounded text-sm border transition-colors
            ${currentLevel === null
              ? 'bg-blue-500 text-white border-blue-500'
              : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
            }
          `}
        >
          全部
        </button>
        {LEVELS.map(level => (
          <button
            key={level}
            onClick={() => onChange(level)}
            className={`
              px-3 py-1 rounded text-sm border transition-colors
              ${currentLevel === level
                ? 'bg-blue-500 text-white border-blue-500'
                : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
              }
            `}
          >
            Level {level} ({levelCounts[level]})
          </button>
        ))}
      </div>
    </div>
  );
});

LevelFilter.displayName = 'LevelFilter';

/** 统计信息组件 */
const Statistics: React.FC<{
  totalPoints: number;
  colorCounts: Record<ColorType, number>;
  levelCounts: Record<DataLevel, number>;
}> = memo(({ totalPoints, colorCounts, levelCounts }) => {
  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium text-gray-700">A组数据统计</h3>
      <div className="grid grid-cols-2 gap-2 text-sm">
        <div className="bg-gray-50 p-2 rounded">
          <span className="text-gray-600">总数据点:</span>
          <span className="ml-1 font-medium">{totalPoints}</span>
        </div>
        <div className="bg-gray-50 p-2 rounded">
          <span className="text-gray-600">颜色种类:</span>
          <span className="ml-1 font-medium">{Object.keys(colorCounts).filter(c => colorCounts[c as ColorType] > 0).length}</span>
        </div>
      </div>
    </div>
  );
});

Statistics.displayName = 'Statistics';

// ===== 主组件 =====

const GroupAPanel: React.FC<GroupAPanelProps> = ({
  className = '',
  style,
  showColorButtons = true,
  showLevelFilter = true,
  showStatistics = true,
  onColorToggle,
  onLevelChange,
}) => {
  const { matrixData } = useMatrixStore();
  
  // 计算统计数据
  const statistics = useMemo(() => {
    return {
      totalPoints: matrixData.metadata.totalPoints,
      colorCounts: matrixData.metadata.colorCounts,
      levelCounts: matrixData.metadata.levelCounts,
    };
  }, [matrixData.metadata]);
  
  // 处理颜色切换
  const handleColorToggle = (color: ColorType) => {
    onColorToggle?.(color);
  };
  
  // 处理层级变更
  const handleLevelChange = (level: DataLevel | null) => {
    onLevelChange?.(level);
  };
  
  return (
    <div className={`group-a-panel space-y-4 ${className}`} style={style}>
      {/* 标题 */}
      <div className="border-b pb-2">
        <h2 className="text-lg font-semibold text-gray-800">A组数据控制</h2>
        <p className="text-sm text-gray-600">基础数据驱动系统</p>
      </div>
      
      {/* 颜色按钮 */}
      {showColorButtons && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-700">颜色选择</h3>
          <div className="grid grid-cols-4 gap-2">
            {COLORS.map(color => (
              <ColorButton
                key={color}
                color={color}
                count={statistics.colorCounts[color]}
                onClick={() => handleColorToggle(color)}
              />
            ))}
          </div>
        </div>
      )}
      
      {/* 层级过滤器 */}
      {showLevelFilter && (
        <LevelFilter
          currentLevel={null}
          levelCounts={statistics.levelCounts}
          onChange={handleLevelChange}
        />
      )}
      
      {/* 统计信息 */}
      {showStatistics && (
        <Statistics
          totalPoints={statistics.totalPoints}
          colorCounts={statistics.colorCounts}
          levelCounts={statistics.levelCounts}
        />
      )}
    </div>
  );
};

GroupAPanel.displayName = 'GroupAPanel';

export default GroupAPanel;
export { GroupAPanel };
export type { GroupAPanelProps };
