import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./core/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      // 矩阵专用配置
      spacing: {
        'matrix': '33px',
      },
      colors: {
        matrix: {
          red: '#ef4444',
          cyan: '#06b6d4',
          yellow: '#eab308',
          purple: '#a855f7',
          orange: '#f97316',
          green: '#22c55e',
          blue: '#3b82f6',
          pink: '#ec4899',
        }
      },
      animation: {
        'matrix-hover': 'scale 0.1s ease-in-out',
      },
      keyframes: {
        scale: {
          '0%': { transform: 'scale(1)' },
          '100%': { transform: 'scale(1.05)' },
        }
      }
    }
  },
  plugins: [],
};

export default config;
